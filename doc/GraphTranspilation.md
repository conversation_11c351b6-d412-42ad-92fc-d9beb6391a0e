# Lean4 to Graph Transpilation Guide

This document provides a comprehensive guide for transpiling Mathlib4 into a purely graph-based axiomatic representation. It covers the key components of Lean4's parser, elaborator, and compiler that are relevant to this transformation.

## Overview

The goal is to represent Mathlib4's mathematical content as a graph where:
- **Nodes** represent mathematical objects (theorems, definitions, axioms, types)
- **Edges** represent relationships (dependencies, applications, type relationships)
- **Attributes** preserve semantic information (types, proofs, metadata)

## Transformation Pipeline

```
Source Code → Parser → Syntax Trees → Elaborator → Kernel Expressions → Graph
```

### Stage 1: Parsing (Source → Syntax)
**Key Files**: `src/Lean/Parser/Module.lean`, `src/Lean/Parser/Attr.lean`, `src/Lean/Syntax.lean`

The parser transforms source code into structured syntax trees:
- **Module Parser**: Handles file-level structure and imports
- **Attribute Parser**: Processes metadata and annotations
- **Syntax Trees**: Hierarchical representation preserving source structure

**Graph Relevance**: 
- Import relationships form the backbone of module dependencies
- Syntax structure provides initial hierarchical organization
- Source locations enable traceability from graph back to code

### Stage 2: Elaboration (Syntax → Expressions)
**Key Files**: `src/Lean/Elab/Term.lean`, `src/Lean/Elab/Command.lean`

The elaborator transforms syntax into typed kernel expressions:
- **Type Checking**: Ensures mathematical correctness
- **Metavariable Resolution**: Fills in implicit information
- **Dependency Resolution**: Makes all references explicit

**Graph Relevance**:
- Reveals true semantic dependencies hidden by syntactic sugar
- Computes complete type information for all expressions
- Exposes the logical structure of proofs and definitions

### Stage 3: Kernel Representation (Expressions → Axiomatic Form)
**Key Files**: `src/Lean/Expr.lean`, `src/Lean/Environment.lean`, `src/kernel/`

The kernel maintains the final axiomatic representation:
- **Expressions**: Core logical constructs (applications, abstractions, constants)
- **Environment**: Global database of all declarations
- **Type System**: Dependent type theory foundation

**Graph Relevance**:
- Kernel expressions are the direct source for graph nodes
- Environment provides the complete declaration database
- Dependencies are explicit and type-checked

## Graph Construction Strategy

### 1. Node Types

#### Declaration Nodes
- **Axioms**: Fundamental assumptions (`Declaration.axiomDecl`)
- **Definitions**: Computational content (`Declaration.defnDecl`) 
- **Theorems**: Proven statements (`Declaration.thmDecl`)
- **Inductives**: Type definitions (`Declaration.inductDecl`)

#### Expression Nodes
- **Constants**: References to declarations (`Expr.const`)
- **Applications**: Function calls (`Expr.app`)
- **Abstractions**: Lambda/Pi types (`Expr.lam`, `Expr.forallE`)
- **Literals**: Concrete values (`Expr.lit`)

### 2. Edge Types

#### Dependency Edges
- From declarations to all constants they reference
- Extracted via `Expr.getUsedConstants`
- Forms the primary dependency graph

#### Structural Edges
- Function-argument relationships in applications
- Binder-body relationships in abstractions
- Type-term relationships

#### Module Edges
- Import relationships between files
- Namespace hierarchies

### 3. Node Attributes

#### Semantic Information
- **Types**: Complete type expressions for all nodes
- **Values**: Definition bodies and proof terms
- **Universe Levels**: Type hierarchy information

#### Metadata
- **Names**: Fully qualified identifiers
- **Source Locations**: Traceability to original code
- **Attributes**: Lean attributes and annotations

## Implementation Approach

### Phase 1: Environment Analysis
```lean
-- Extract all declarations
def extractDeclarations (env : Environment) : List (Name × ConstantInfo) :=
  env.constants.fold [] fun acc name info => (name, info) :: acc

-- Analyze dependencies
def getDependencies (info : ConstantInfo) : List Name :=
  let typeDeps := info.type.getUsedConstants
  let valueDeps := info.value?.map (·.getUsedConstants) |>.getD []
  typeDeps ++ valueDeps
```

### Phase 2: Graph Construction
```lean
structure GraphNode where
  name : Name
  kind : DeclarationKind  -- axiom, defn, thm, etc.
  type : Expr
  value? : Option Expr
  dependencies : List Name
  sourceInfo : SourceInfo

def buildGraph (env : Environment) : Graph GraphNode :=
  -- Convert environment to explicit graph representation
```

### Phase 3: Analysis and Optimization
- Remove redundant information
- Optimize for specific analysis tasks
- Add derived relationships (transitive dependencies, etc.)

## Key Insights for Implementation

### 1. Environment as Graph Source
The Lean environment is already essentially a graph:
- Nodes: All declarations in `env.constants`
- Edges: Dependency relationships
- The transpilation primarily makes this structure explicit

### 2. Expression Traversal
Use `Expr.fold` and similar functions to systematically extract:
- All constant references (dependencies)
- Structural relationships
- Type information

### 3. Incremental Processing
Lean's concurrent elaboration system enables:
- Parallel processing of large codebases
- Incremental graph construction
- Efficient handling of Mathlib4's scale

### 4. Preservation of Semantics
The graph must preserve:
- Logical correctness (type relationships)
- Dependency ordering (topological structure)
- Proof structure (inference chains)

## Tools and Utilities

### Environment Queries
- `Environment.find`: Look up specific declarations
- `Environment.constants`: Access complete declaration map
- `Environment.imports`: Module dependency information

### Expression Analysis
- `Expr.getUsedConstants`: Extract all dependencies
- `Expr.fold`: Systematic expression traversal
- `Expr.replace`: Transform expressions during analysis

### Type Information
- `inferType`: Compute types for expressions
- `isDefEq`: Check definitional equality
- Universe level analysis for type hierarchy

## Conclusion

The Lean4 architecture provides excellent support for graph transpilation:
- Clean separation between parsing, elaboration, and kernel
- Explicit dependency tracking throughout the pipeline
- Rich metadata preservation
- Concurrent processing capabilities

The key insight is that Lean's environment already contains the graph structure - the transpilation process primarily involves extracting and making this structure explicit for analysis purposes.
